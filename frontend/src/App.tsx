
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import Platforms from './components/Platforms';
import Architectures from './components/Architectures';
import Releases from './components/Releases';
import UpdateChecks from './components/UpdateChecks';
import DownloadRecords from './components/DownloadRecords';
import Login from './components/Login';
import Register from './components/Register';
import ProtectedRoute from './components/ProtectedRoute';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* 公开路由 */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* 受保护的路由 */}
          <Route path="/*" element={
            <ProtectedRoute>
              <Layout>
                <Routes>
                  {/* 需要管理员权限的路由 */}
                  <Route path="/" element={
                    <ProtectedRoute requireAdmin={true}>
                      <Dashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/platforms" element={
                    <ProtectedRoute requireAdmin={true}>
                      <Platforms />
                    </ProtectedRoute>
                  } />
                  <Route path="/architectures" element={
                    <ProtectedRoute requireAdmin={true}>
                      <Architectures />
                    </ProtectedRoute>
                  } />
                  <Route path="/releases" element={
                    <ProtectedRoute requireAdmin={true}>
                      <Releases />
                    </ProtectedRoute>
                  } />
                  <Route path="/update-checks" element={
                    <ProtectedRoute requireAdmin={true}>
                      <UpdateChecks />
                    </ProtectedRoute>
                  } />
                  <Route path="/download-records" element={
                    <ProtectedRoute requireAdmin={true}>
                      <DownloadRecords />
                    </ProtectedRoute>
                  } />
                </Routes>
              </Layout>
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
